// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		041C2F762E34FE4500177F1B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 041C2F522E34FE3400177F1B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 041C2F592E34FE3400177F1B;
			remoteInfo = LoadURL;
		};
		041C2F802E34FE4500177F1B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 041C2F522E34FE3400177F1B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 041C2F592E34FE3400177F1B;
			remoteInfo = LoadURL;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		041C2F5A2E34FE3500177F1B /* LoadURL.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = LoadURL.app; sourceTree = BUILT_PRODUCTS_DIR; };
		041C2F752E34FE4500177F1B /* LoadURLTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LoadURLTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		041C2F7F2E34FE4500177F1B /* LoadURLUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LoadURLUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		041C2F872E34FE4500177F1B /* Exceptions for "LoadURL" folder in "LoadURL" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 041C2F592E34FE3400177F1B /* LoadURL */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		041C2F5C2E34FE3500177F1B /* LoadURL */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				041C2F872E34FE4500177F1B /* Exceptions for "LoadURL" folder in "LoadURL" target */,
			);
			path = LoadURL;
			sourceTree = "<group>";
		};
		041C2F782E34FE4500177F1B /* LoadURLTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LoadURLTests;
			sourceTree = "<group>";
		};
		041C2F822E34FE4500177F1B /* LoadURLUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LoadURLUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		041C2F572E34FE3400177F1B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		041C2F722E34FE4500177F1B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		041C2F7C2E34FE4500177F1B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		041C2F512E34FE3400177F1B = {
			isa = PBXGroup;
			children = (
				041C2F5C2E34FE3500177F1B /* LoadURL */,
				041C2F782E34FE4500177F1B /* LoadURLTests */,
				041C2F822E34FE4500177F1B /* LoadURLUITests */,
				041C2F5B2E34FE3500177F1B /* Products */,
			);
			sourceTree = "<group>";
		};
		041C2F5B2E34FE3500177F1B /* Products */ = {
			isa = PBXGroup;
			children = (
				041C2F5A2E34FE3500177F1B /* LoadURL.app */,
				041C2F752E34FE4500177F1B /* LoadURLTests.xctest */,
				041C2F7F2E34FE4500177F1B /* LoadURLUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		041C2F592E34FE3400177F1B /* LoadURL */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 041C2F882E34FE4500177F1B /* Build configuration list for PBXNativeTarget "LoadURL" */;
			buildPhases = (
				041C2F562E34FE3400177F1B /* Sources */,
				041C2F572E34FE3400177F1B /* Frameworks */,
				041C2F582E34FE3400177F1B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				041C2F5C2E34FE3500177F1B /* LoadURL */,
			);
			name = LoadURL;
			packageProductDependencies = (
			);
			productName = LoadURL;
			productReference = 041C2F5A2E34FE3500177F1B /* LoadURL.app */;
			productType = "com.apple.product-type.application";
		};
		041C2F742E34FE4500177F1B /* LoadURLTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 041C2F8D2E34FE4500177F1B /* Build configuration list for PBXNativeTarget "LoadURLTests" */;
			buildPhases = (
				041C2F712E34FE4500177F1B /* Sources */,
				041C2F722E34FE4500177F1B /* Frameworks */,
				041C2F732E34FE4500177F1B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				041C2F772E34FE4500177F1B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				041C2F782E34FE4500177F1B /* LoadURLTests */,
			);
			name = LoadURLTests;
			packageProductDependencies = (
			);
			productName = LoadURLTests;
			productReference = 041C2F752E34FE4500177F1B /* LoadURLTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		041C2F7E2E34FE4500177F1B /* LoadURLUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 041C2F902E34FE4500177F1B /* Build configuration list for PBXNativeTarget "LoadURLUITests" */;
			buildPhases = (
				041C2F7B2E34FE4500177F1B /* Sources */,
				041C2F7C2E34FE4500177F1B /* Frameworks */,
				041C2F7D2E34FE4500177F1B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				041C2F812E34FE4500177F1B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				041C2F822E34FE4500177F1B /* LoadURLUITests */,
			);
			name = LoadURLUITests;
			packageProductDependencies = (
			);
			productName = LoadURLUITests;
			productReference = 041C2F7F2E34FE4500177F1B /* LoadURLUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		041C2F522E34FE3400177F1B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					041C2F592E34FE3400177F1B = {
						CreatedOnToolsVersion = 16.4;
					};
					041C2F742E34FE4500177F1B = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 041C2F592E34FE3400177F1B;
					};
					041C2F7E2E34FE4500177F1B = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 041C2F592E34FE3400177F1B;
					};
				};
			};
			buildConfigurationList = 041C2F552E34FE3400177F1B /* Build configuration list for PBXProject "LoadURL" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 041C2F512E34FE3400177F1B;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 041C2F5B2E34FE3500177F1B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				041C2F592E34FE3400177F1B /* LoadURL */,
				041C2F742E34FE4500177F1B /* LoadURLTests */,
				041C2F7E2E34FE4500177F1B /* LoadURLUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		041C2F582E34FE3400177F1B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		041C2F732E34FE4500177F1B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		041C2F7D2E34FE4500177F1B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		041C2F562E34FE3400177F1B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		041C2F712E34FE4500177F1B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		041C2F7B2E34FE4500177F1B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		041C2F772E34FE4500177F1B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 041C2F592E34FE3400177F1B /* LoadURL */;
			targetProxy = 041C2F762E34FE4500177F1B /* PBXContainerItemProxy */;
		};
		041C2F812E34FE4500177F1B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 041C2F592E34FE3400177F1B /* LoadURL */;
			targetProxy = 041C2F802E34FE4500177F1B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		041C2F892E34FE4500177F1B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AWDS987LT7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LoadURL/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.devli.demo.LoadURL;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		041C2F8A2E34FE4500177F1B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AWDS987LT7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LoadURL/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.devli.demo.LoadURL;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		041C2F8B2E34FE4500177F1B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = AWDS987LT7;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		041C2F8C2E34FE4500177F1B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = AWDS987LT7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		041C2F8E2E34FE4500177F1B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AWDS987LT7;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.devli.demo.LoadURLTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LoadURL.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LoadURL";
			};
			name = Debug;
		};
		041C2F8F2E34FE4500177F1B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AWDS987LT7;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.devli.demo.LoadURLTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LoadURL.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LoadURL";
			};
			name = Release;
		};
		041C2F912E34FE4500177F1B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AWDS987LT7;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.devli.demo.LoadURLUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = LoadURL;
			};
			name = Debug;
		};
		041C2F922E34FE4500177F1B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AWDS987LT7;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.devli.demo.LoadURLUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = LoadURL;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		041C2F552E34FE3400177F1B /* Build configuration list for PBXProject "LoadURL" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				041C2F8B2E34FE4500177F1B /* Debug */,
				041C2F8C2E34FE4500177F1B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		041C2F882E34FE4500177F1B /* Build configuration list for PBXNativeTarget "LoadURL" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				041C2F892E34FE4500177F1B /* Debug */,
				041C2F8A2E34FE4500177F1B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		041C2F8D2E34FE4500177F1B /* Build configuration list for PBXNativeTarget "LoadURLTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				041C2F8E2E34FE4500177F1B /* Debug */,
				041C2F8F2E34FE4500177F1B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		041C2F902E34FE4500177F1B /* Build configuration list for PBXNativeTarget "LoadURLUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				041C2F912E34FE4500177F1B /* Debug */,
				041C2F922E34FE4500177F1B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 041C2F522E34FE3400177F1B /* Project object */;
}
